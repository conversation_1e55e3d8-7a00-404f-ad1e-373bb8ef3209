import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_dimensions.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/app_utils.dart';

/// A custom gradient button with animations
class GradientButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final Gradient? gradient;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool isLoading;
  final Widget? icon;
  final TextStyle? textStyle;
  final Color? shadowColor;
  final double elevation;
  
  const GradientButton({
    super.key,
    required this.text,
    this.onPressed,
    this.gradient,
    this.width,
    this.height = AppDimensions.buttonHeight,
    this.padding,
    this.margin,
    this.borderRadius = AppDimensions.radiusMd,
    this.isLoading = false,
    this.icon,
    this.textStyle,
    this.shadowColor,
    this.elevation = AppDimensions.cardElevation,
  });

  @override
  State<GradientButton> createState() => _GradientButtonState();
}

class _GradientButtonState extends State<GradientButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final gradient = widget.gradient ?? AppColors.primaryGradient;
    final textStyle = widget.textStyle ?? AppTextStyles.buttonText;
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      child: AnimatedScale(
        scale: _isPressed ? 0.95 : 1.0,
        duration: const Duration(milliseconds: 100),
        child: Container(
          decoration: BoxDecoration(
            gradient: isEnabled ? gradient : null,
            color: isEnabled ? null : AppColors.grey300,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            boxShadow: isEnabled ? [
              BoxShadow(
                color: widget.shadowColor ?? AppColors.primaryBlue.withOpacity(0.3),
                blurRadius: widget.elevation * 2,
                offset: Offset(0, widget.elevation),
              ),
            ] : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: isEnabled ? () {
                AppUtils.hapticFeedback();
                widget.onPressed?.call();
              } : null,
              onTapDown: isEnabled ? (_) => setState(() => _isPressed = true) : null,
              onTapUp: isEnabled ? (_) => setState(() => _isPressed = false) : null,
              onTapCancel: isEnabled ? () => setState(() => _isPressed = false) : null,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              child: Container(
                padding: widget.padding ?? const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingLg,
                  vertical: AppDimensions.paddingMd,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.isLoading) ...[
                      SizedBox(
                        width: AppDimensions.iconSm,
                        height: AppDimensions.iconSm,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            textStyle.color ?? AppColors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppDimensions.spacingSm),
                    ] else if (widget.icon != null) ...[
                      widget.icon!,
                      const SizedBox(width: AppDimensions.spacingSm),
                    ],
                    Text(
                      widget.text,
                      style: textStyle.copyWith(
                        color: isEnabled 
                            ? textStyle.color ?? AppColors.white
                            : AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    ).animate(target: isEnabled ? 1 : 0)
     .fadeIn(duration: 200.ms)
     .scale(begin: const Offset(0.8, 0.8));
  }
}
