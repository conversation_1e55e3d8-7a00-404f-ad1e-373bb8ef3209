/// App-wide constants
class AppConstants {
  // App Info
  static const String appName = 'EduWriter AI';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'AI-powered educational content generator';
  
  // Content Types
  static const String contentTypeParagraph = 'paragraph';
  static const String contentTypeEssay = 'essay';
  static const String contentTypeComposition = 'composition';
  static const String contentTypeLetter = 'letter';
  
  // Content Type Display Names
  static const Map<String, String> contentTypeNames = {
    contentTypeParagraph: 'Paragraph',
    contentTypeEssay: 'Essay',
    contentTypeComposition: 'Composition',
    contentTypeLetter: 'Letter',
  };
  
  // Content Type Descriptions
  static const Map<String, String> contentTypeDescriptions = {
    contentTypeParagraph: 'Generate well-structured paragraphs on any topic',
    contentTypeEssay: 'Create comprehensive essays with introduction, body, and conclusion',
    contentTypeComposition: 'Write creative compositions and stories',
    contentTypeLetter: 'Compose formal and informal letters',
  };
  
  // Grade Levels
  static const List<String> gradeLevels = [
    'Grade 1',
    'Grade 2',
    'Grade 3',
    'Grade 4',
    'Grade 5',
    'Grade 6',
    'Grade 7',
    'Grade 8',
    'Grade 9',
    'Grade 10',
    'Grade 11',
    'Grade 12',
  ];
  
  // Content Lengths
  static const List<String> contentLengths = [
    'Short (100-200 words)',
    'Medium (200-400 words)',
    'Long (400-600 words)',
    'Extra Long (600+ words)',
  ];
  
  // Writing Tones
  static const List<String> writingTones = [
    'Formal',
    'Informal',
    'Academic',
    'Creative',
    'Persuasive',
    'Descriptive',
    'Narrative',
  ];
  
  // Storage Keys
  static const String keyThemeMode = 'theme_mode';
  static const String keyUserName = 'user_name';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyGenerationCount = 'generation_count';
  
  // Animation Durations
  static const Duration animationDurationFast = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationSlow = Duration(milliseconds: 500);
  
  // API Configuration (placeholder)
  static const String apiBaseUrl = 'https://api.eduwriter.ai';
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Limits
  static const int maxTopicLength = 200;
  static const int maxInstructionsLength = 500;
  static const int maxSavedContent = 100;
  
  // Default Values
  static const String defaultGradeLevel = 'Grade 8';
  static const String defaultContentLength = 'Medium (200-400 words)';
  static const String defaultWritingTone = 'Formal';
}
