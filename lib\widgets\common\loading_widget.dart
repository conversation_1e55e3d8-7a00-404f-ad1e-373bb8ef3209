import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_dimensions.dart';
import '../../core/theme/app_text_styles.dart';

/// A modern loading widget with animations
class LoadingWidget extends StatelessWidget {
  final String? message;
  final double size;
  final Color? color;
  final bool showMessage;
  
  const LoadingWidget({
    super.key,
    this.message,
    this.size = AppDimensions.loadingIndicatorSize,
    this.color,
    this.showMessage = true,
  });

  @override
  Widget build(BuildContext context) {
    final loadingColor = color ?? AppColors.primaryBlue;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated loading indicator
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(size / 2),
            ),
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.white,
              ),
            ),
          ).animate(onPlay: (controller) => controller.repeat())
           .rotate(duration: 1000.ms)
           .then()
           .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.2, 1.2))
           .then()
           .scale(begin: const Offset(1.2, 1.2), end: const Offset(0.8, 0.8)),
          
          if (showMessage) ...[
            const SizedBox(height: AppDimensions.spacingLg),
            Text(
              message ?? 'Loading...',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ).animate(onPlay: (controller) => controller.repeat(reverse: true))
             .fadeIn(duration: 1000.ms)
             .then()
             .fadeOut(duration: 1000.ms),
          ],
        ],
      ),
    );
  }
}

/// A simple loading overlay
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingMessage;
  
  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: AppColors.black.withOpacity(0.5),
            child: LoadingWidget(
              message: loadingMessage,
            ),
          ).animate()
           .fadeIn(duration: 200.ms),
      ],
    );
  }
}
