/// App route names and navigation constants
class AppRoutes {
  // Route names
  static const String home = '/';
  static const String contentTypeSelection = '/content-type-selection';
  static const String contentGeneration = '/content-generation';
  static const String generatedContent = '/generated-content';
  static const String savedContent = '/saved-content';
  static const String settings = '/settings';
  static const String loading = '/loading';
  
  // Route arguments keys
  static const String argContentType = 'content_type';
  static const String argGeneratedContent = 'generated_content';
  static const String argContentId = 'content_id';
  static const String argFormData = 'form_data';
  
  // Navigation helpers
  static String contentGenerationWithType(String contentType) {
    return '$contentGeneration?$argContentType=$contentType';
  }
  
  static String generatedContentWithId(String contentId) {
    return '$generatedContent?$argContentId=$contentId';
  }
}
