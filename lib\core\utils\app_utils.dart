import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// Utility functions for the app
class AppUtils {
  // Date formatting
  static String formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }
  
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('MMM dd, yyyy • hh:mm a').format(dateTime);
  }
  
  static String formatTime(DateTime time) {
    return DateFormat('hh:mm a').format(time);
  }
  
  // Text utilities
  static int getWordCount(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }
  
  static int getReadingTime(String text) {
    const int wordsPerMinute = 200;
    final int wordCount = getWordCount(text);
    return (wordCount / wordsPerMinute).ceil();
  }
  
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }
  
  static String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
  
  // Validation
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  static bool isNotEmpty(String? text) {
    return text != null && text.trim().isNotEmpty;
  }
  
  // UI utilities
  static void showSnackBar(BuildContext context, String message, {
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }
  
  static void hapticFeedback() {
    HapticFeedback.lightImpact();
  }
  
  // Navigation utilities
  static void pushScreen(BuildContext context, Widget screen) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => screen),
    );
  }
  
  static void pushReplacementScreen(BuildContext context, Widget screen) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => screen),
    );
  }
  
  static void popScreen(BuildContext context) {
    Navigator.of(context).pop();
  }
  
  // Color utilities
  static Color getContentTypeColor(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'paragraph':
        return const Color(0xFF4CAF50);
      case 'essay':
        return const Color(0xFF9C27B0);
      case 'composition':
        return const Color(0xFFFF9800);
      case 'letter':
        return const Color(0xFFE91E63);
      default:
        return const Color(0xFF2196F3);
    }
  }
  
  static IconData getContentTypeIcon(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'paragraph':
        return Icons.article_outlined;
      case 'essay':
        return Icons.description_outlined;
      case 'composition':
        return Icons.create_outlined;
      case 'letter':
        return Icons.mail_outline;
      default:
        return Icons.text_snippet_outlined;
    }
  }
  
  // File utilities
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }
  
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  // Debug utilities
  static void debugLog(String message) {
    debugPrint('[EduWriter AI] $message');
  }
  
  // Theme utilities
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }
  
  static Color getAdaptiveColor(BuildContext context, Color lightColor, Color darkColor) {
    return isDarkMode(context) ? darkColor : lightColor;
  }
}
