import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_dimensions.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/app_utils.dart';
import 'glass_card.dart';

/// A card widget for displaying content types
class ContentTypeCard extends StatefulWidget {
  final String title;
  final String description;
  final String contentType;
  final VoidCallback? onTap;
  final bool isSelected;
  final EdgeInsetsGeometry? margin;
  
  const ContentTypeCard({
    super.key,
    required this.title,
    required this.description,
    required this.contentType,
    this.onTap,
    this.isSelected = false,
    this.margin,
  });

  @override
  State<ContentTypeCard> createState() => _ContentTypeCardState();
}

class _ContentTypeCardState extends State<ContentTypeCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final contentTypeColor = AppUtils.getContentTypeColor(widget.contentType);
    final contentTypeIcon = AppUtils.getContentTypeIcon(widget.contentType);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: widget.margin ?? const EdgeInsets.only(bottom: AppDimensions.spacingMd),
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()
            ..scale(_isHovered ? 1.02 : 1.0),
          child: GlassCard(
            onTap: () {
              AppUtils.hapticFeedback();
              widget.onTap?.call();
            },
            backgroundColor: widget.isSelected 
                ? contentTypeColor.withOpacity(0.1)
                : null,
            borderColor: widget.isSelected 
                ? contentTypeColor.withOpacity(0.5)
                : null,
            child: Container(
              height: AppDimensions.contentTypeCardHeight,
              child: Row(
                children: [
                  // Icon section
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          contentTypeColor,
                          contentTypeColor.withOpacity(0.7),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
                      boxShadow: [
                        BoxShadow(
                          color: contentTypeColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      contentTypeIcon,
                      color: AppColors.white,
                      size: AppDimensions.iconLg,
                    ),
                  ),
                  
                  const SizedBox(width: AppDimensions.spacingMd),
                  
                  // Content section
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.title,
                          style: AppTextStyles.cardTitle.copyWith(
                            color: widget.isSelected 
                                ? contentTypeColor
                                : null,
                          ),
                        ),
                        const SizedBox(height: AppDimensions.spacing2xs),
                        Text(
                          widget.description,
                          style: AppTextStyles.cardSubtitle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  // Arrow icon
                  Container(
                    padding: const EdgeInsets.all(AppDimensions.paddingXs),
                    decoration: BoxDecoration(
                      color: contentTypeColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
                    ),
                    child: Icon(
                      Icons.arrow_forward_ios,
                      color: contentTypeColor,
                      size: AppDimensions.iconSm,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ).animate(delay: (widget.contentType.hashCode % 4 * 100).ms)
     .fadeIn(duration: 600.ms)
     .slideX(begin: 0.3, end: 0);
  }
}
