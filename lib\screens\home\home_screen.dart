import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_dimensions.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../widgets/common/glass_card.dart';
import '../../widgets/common/content_type_card.dart';
import '../../widgets/common/gradient_button.dart';

/// Home screen with modern design and content type selection
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String? selectedContentType;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Safe<PERSON><PERSON>(
          child: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 120,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    AppConstants.appName,
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  centerTitle: true,
                ),
                actions: [
                  IconButton(
                    onPressed: () {
                      // TODO: Navigate to settings
                      AppUtils.showSnackBar(context, 'Settings coming soon!');
                    },
                    icon: const Icon(
                      Icons.settings_outlined,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
              
              // Content
              SliverPadding(
                padding: const EdgeInsets.all(AppDimensions.screenPaddingHorizontal),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // Welcome Section
                    _buildWelcomeSection(),
                    
                    const SizedBox(height: AppDimensions.spacingXl),
                    
                    // Content Types Section
                    _buildContentTypesSection(),
                    
                    const SizedBox(height: AppDimensions.spacingXl),
                    
                    // Quick Stats Section
                    _buildQuickStatsSection(),
                    
                    const SizedBox(height: AppDimensions.spacingXl),
                    
                    // Continue Button
                    if (selectedContentType != null) _buildContinueButton(),
                    
                    const SizedBox(height: AppDimensions.spacingXl),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildWelcomeSection() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingMd),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: AppColors.white,
                  size: AppDimensions.iconLg,
                ),
              ),
              const SizedBox(width: AppDimensions.spacingMd),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to EduWriter AI',
                      style: AppTextStyles.titleLarge,
                    ),
                    const SizedBox(height: AppDimensions.spacing2xs),
                    Text(
                      'Create amazing educational content with AI',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate()
     .fadeIn(duration: 600.ms)
     .slideY(begin: 0.3, end: 0);
  }
  
  Widget _buildContentTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Content Type',
          style: AppTextStyles.headlineSmall,
        ).animate()
         .fadeIn(delay: 200.ms, duration: 600.ms)
         .slideX(begin: -0.3, end: 0),
        
        const SizedBox(height: AppDimensions.spacingMd),
        
        ...AppConstants.contentTypeNames.entries.map((entry) {
          final contentType = entry.key;
          final name = entry.value;
          final description = AppConstants.contentTypeDescriptions[contentType] ?? '';
          
          return ContentTypeCard(
            title: name,
            description: description,
            contentType: contentType,
            isSelected: selectedContentType == contentType,
            onTap: () {
              setState(() {
                selectedContentType = contentType;
              });
            },
          );
        }).toList(),
      ],
    );
  }
  
  Widget _buildQuickStatsSection() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Stats',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: AppDimensions.spacingMd),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.article_outlined,
                  label: 'Generated',
                  value: '0',
                  color: AppColors.primaryBlue,
                ),
              ),
              const SizedBox(width: AppDimensions.spacingMd),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.favorite_outline,
                  label: 'Favorites',
                  value: '0',
                  color: AppColors.error,
                ),
              ),
              const SizedBox(width: AppDimensions.spacingMd),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.history,
                  label: 'Recent',
                  value: '0',
                  color: AppColors.accent,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate()
     .fadeIn(delay: 400.ms, duration: 600.ms)
     .slideY(begin: 0.3, end: 0);
  }
  
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppDimensions.paddingSm),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
          ),
          child: Icon(
            icon,
            color: color,
            size: AppDimensions.iconMd,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingXs),
        Text(
          value,
          style: AppTextStyles.titleMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.grey600,
          ),
        ),
      ],
    );
  }
  
  Widget _buildContinueButton() {
    return GradientButton(
      text: 'Continue with ${AppConstants.contentTypeNames[selectedContentType]}',
      onPressed: () {
        // TODO: Navigate to content generation screen
        AppUtils.showSnackBar(
          context, 
          'Generating ${AppConstants.contentTypeNames[selectedContentType]}...',
        );
      },
      width: double.infinity,
      icon: const Icon(
        Icons.arrow_forward,
        color: AppColors.white,
        size: AppDimensions.iconSm,
      ),
    ).animate()
     .fadeIn(delay: 100.ms, duration: 400.ms)
     .slideY(begin: 0.5, end: 0);
  }
}
