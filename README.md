# EduWriter AI

An AI-powered educational content generator built with Flutter. Create paragraphs, essays, compositions, and letters with modern design and smooth animations.

## 🎨 Features

- **Modern Design System**: Glassmorphism effects, gradient buttons, and smooth animations
- **Content Types**: Support for paragraphs, essays, compositions, and letters
- **Theme Support**: Light/Dark theme with system preference detection
- **Responsive Design**: Adaptive layouts for different screen sizes
- **State Management**: Provider pattern for clean architecture

## 📱 Screenshots

*Screenshots will be added as development progresses*

## 🏗️ Project Structure

```
lib/
├── core/
│   ├── constants/          # App constants and configuration
│   ├── navigation/         # Route definitions and navigation
│   ├── theme/             # Theme system (colors, typography, dimensions)
│   └── utils/             # Utility functions and helpers
├── providers/             # State management providers
├── screens/               # App screens and pages
│   └── home/             # Home screen implementation
├── widgets/               # Reusable UI components
│   └── common/           # Common widgets (cards, buttons, etc.)
└── main.dart             # App entry point
```

## 🎯 Development Phases

### ✅ Phase 1: Foundation & Theme (Completed)
- [x] Project structure setup
- [x] Modern theme system with Material 3
- [x] Custom widgets library (GlassCard, GradientButton, etc.)
- [x] Navigation setup
- [x] Home screen with content type selection

### 🚧 Phase 2: Core Screens (In Progress)
- [ ] Content generation form with dynamic fields
- [ ] API integration for AI content generation
- [ ] Loading states and animations
- [ ] Generated content display

### 📋 Phase 3: Content Management
- [ ] Save/favorite functionality
- [ ] Content history and search
- [ ] PDF export capabilities
- [ ] Share features

### 🎨 Phase 4: Polish & Features
- [ ] Settings screen
- [ ] Advanced animations
- [ ] Tablet layouts
- [ ] Performance optimization

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.5.1 or higher)
- Dart SDK
- Chrome (for web development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd edu_writer_ai
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
# For web
flutter run -d chrome

# For mobile (if emulator is available)
flutter run
```

## 📦 Dependencies

### Core Dependencies
- `flutter`: Flutter SDK
- `provider`: State management
- `google_fonts`: Typography system
- `flutter_animate`: Smooth animations
- `shared_preferences`: Local storage
- `hive`: Local database
- `uuid`: Unique identifiers
- `intl`: Internationalization

### Development Dependencies
- `flutter_test`: Testing framework
- `flutter_lints`: Code analysis
- `hive_generator`: Code generation for Hive
- `build_runner`: Build system

## 🎨 Design System

### Color Palette
- **Primary**: Modern blue gradient (#2196F3 to #1976D2)
- **Secondary**: Cyan accent (#00BCD4)
- **Content Types**:
  - Paragraph: Green (#4CAF50)
  - Essay: Purple (#9C27B0)
  - Composition: Orange (#FF9800)
  - Letter: Pink (#E91E63)

### Typography
- **Font Family**: Poppins (Google Fonts)
- **Scale**: Material 3 typography scale
- **Weights**: 400 (Regular), 500 (Medium), 600 (SemiBold)

### Spacing
- **Grid System**: 8px base unit
- **Spacing Scale**: 4px, 8px, 12px, 16px, 24px, 32px, 40px, 48px

## 🧪 Testing

Run tests with:
```bash
flutter test
```

## 📱 Platform Support

- ✅ Web (Chrome, Firefox, Safari)
- ✅ Android (API 21+)
- ✅ iOS (iOS 12+)
- ⚠️ Windows (requires Visual Studio toolchain)
- ⚠️ macOS (requires Xcode)
- ⚠️ Linux (requires build tools)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔮 Roadmap

- [ ] AI integration with multiple providers
- [ ] Offline content generation
- [ ] Multi-language support
- [ ] Advanced content customization
- [ ] Collaboration features
- [ ] Analytics and insights

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
#   E d u W r i t e r A I - a p p 
 
 