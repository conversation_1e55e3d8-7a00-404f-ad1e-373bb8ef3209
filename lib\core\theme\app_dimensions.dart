/// App dimensions and spacing system based on 8px grid
class AppDimensions {
  // Base unit (8px grid system)
  static const double baseUnit = 8.0;
  
  // Spacing
  static const double spacing2xs = baseUnit * 0.5; // 4px
  static const double spacingXs = baseUnit; // 8px
  static const double spacingSm = baseUnit * 1.5; // 12px
  static const double spacingMd = baseUnit * 2; // 16px
  static const double spacingLg = baseUnit * 3; // 24px
  static const double spacingXl = baseUnit * 4; // 32px
  static const double spacing2xl = baseUnit * 5; // 40px
  static const double spacing3xl = baseUnit * 6; // 48px
  
  // Padding
  static const double paddingXs = spacingXs;
  static const double paddingSm = spacingSm;
  static const double paddingMd = spacingMd;
  static const double paddingLg = spacingLg;
  static const double paddingXl = spacingXl;
  
  // Margins
  static const double marginXs = spacingXs;
  static const double marginSm = spacingSm;
  static const double marginMd = spacingMd;
  static const double marginLg = spacingLg;
  static const double marginXl = spacingXl;
  
  // Border radius
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusMd = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 24.0;
  static const double radiusRound = 50.0;
  
  // Icon sizes
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 48.0;
  static const double icon2xl = 64.0;
  
  // Button dimensions
  static const double buttonHeight = 48.0;
  static const double buttonHeightSm = 36.0;
  static const double buttonHeightLg = 56.0;
  static const double buttonMinWidth = 88.0;
  
  // Card dimensions
  static const double cardElevation = 2.0;
  static const double cardElevationHover = 8.0;
  static const double cardMinHeight = 120.0;
  
  // App bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;
  
  // Bottom navigation
  static const double bottomNavHeight = 80.0;
  
  // Form elements
  static const double inputHeight = 56.0;
  static const double inputBorderWidth = 1.0;
  static const double inputFocusedBorderWidth = 2.0;
  
  // Divider
  static const double dividerThickness = 1.0;
  static const double dividerIndent = spacingMd;
  
  // Screen padding
  static const double screenPaddingHorizontal = spacingMd;
  static const double screenPaddingVertical = spacingMd;
  
  // Content type card dimensions
  static const double contentTypeCardHeight = 140.0;
  static const double contentTypeCardWidth = double.infinity;
  
  // Loading indicator
  static const double loadingIndicatorSize = 40.0;
  
  // Avatar sizes
  static const double avatarSm = 32.0;
  static const double avatarMd = 48.0;
  static const double avatarLg = 64.0;
  
  // Glassmorphism
  static const double glassBlurRadius = 10.0;
  static const double glassBorderWidth = 1.0;
}
